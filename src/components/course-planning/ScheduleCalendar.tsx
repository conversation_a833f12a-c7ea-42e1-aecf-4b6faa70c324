'use client';

import React from 'react';
import { Calendar, Clock, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useCoursePlanning } from '@/contexts/CoursePlanningContext';
import { numToDate, getTotalDaysBetweenDates } from '@/lib/ts/course-planning/schedule-generator';

const DAYS_OF_WEEK = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

const SHIFTS = [
	{
		name: 'Sáng',
		sessions: [1, 2, 3, 4, 5, 6],
		color: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
	},
	{
		name: 'Chiều',
		sessions: [7, 8, 9, 10, 11, 12],
		color: 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
	},
	{
		name: 'Tối',
		sessions: [13, 14, 15, 16],
		color: 'bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800'
	}
];

export function ScheduleCalendar() {
	const { state, getCalendarTableData } = useCoursePlanning();

	if (!state.calendar) {
		return (
			<Card>
				<CardContent className="p-6">
					<div className="text-center text-muted-foreground">
						<Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Vui lòng tải lên file Excel để xem lịch học</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	const calendarData = getCalendarTableData();
	const hasSelectedSubjects = Object.values(state.selectedClasses).some((majorData) =>
		Object.values(majorData).some((subject) => subject.show)
	);

	if (!hasSelectedSubjects) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Lịch học
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-center text-muted-foreground py-8">
						<Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Chọn môn học để xem lịch học</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (!calendarData) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Lịch học
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-center text-muted-foreground py-8">
						<p>Đang tải dữ liệu lịch học...</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	const minDate = numToDate(state.calendar.minDate);
	const maxDate = numToDate(state.calendar.maxDate);

	// Create weekly view
	const weeks: Date[][] = [];
	const currentDate = new Date(minDate);

	// Find the Monday of the first week
	while (currentDate.getDay() !== 1) {
		currentDate.setDate(currentDate.getDate() - 1);
	}

	while (currentDate <= maxDate) {
		const week: Date[] = [];
		for (let i = 0; i < 7; i++) {
			week.push(new Date(currentDate));
			currentDate.setDate(currentDate.getDate() + 1);
		}
		weeks.push(week);
	}

	const getSessionsForDay = (date: Date) => {
		const dayIndex = getTotalDaysBetweenDates(minDate, date);
		if (dayIndex < 0 || dayIndex >= calendarData.data.length) return [];

		return calendarData.data[dayIndex] || [];
	};

	const getSessionsByShift = (sessions: any[][], shiftSessions: number[]) => {
		const shiftItems: any[] = [];

		sessions.forEach((sessionRow) => {
			sessionRow.forEach((session) => {
				// Check if session overlaps with this shift
				const sessionStart = session.startSession;
				const sessionEnd = session.endSession;

				const hasOverlap = shiftSessions.some(
					(shiftSession) => sessionStart <= shiftSession && sessionEnd >= shiftSession
				);

				if (hasOverlap) {
					shiftItems.push(session);
				}
			});
		});

		return shiftItems;
	};

	const getSessionColor = (subjectName: string) => {
		// Generate consistent color based on subject name
		const colors = [
			'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-200 dark:border-blue-700',
			'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-200 dark:border-green-700',
			'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-200 dark:border-purple-700',
			'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-200 dark:border-orange-700',
			'bg-pink-100 text-pink-800 border-pink-200 dark:bg-pink-900/30 dark:text-pink-200 dark:border-pink-700',
			'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-200 dark:border-indigo-700',
			'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-200 dark:border-yellow-700',
			'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-200 dark:border-red-700'
		];

		let hash = 0;
		for (let i = 0; i < subjectName.length; i++) {
			hash = subjectName.charCodeAt(i) + ((hash << 5) - hash);
		}
		return colors[Math.abs(hash) % colors.length];
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Calendar className="h-5 w-5" />
					Lịch học
				</CardTitle>
				<CardDescription>Lịch học được tạo từ các môn đã chọn</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Conflict Warning */}
				{calendarData.totalConflictedSessions > 0 && (
					<Alert variant="destructive">
						<AlertTriangle className="h-4 w-4" />
						<AlertDescription>
							Có {calendarData.totalConflictedSessions} tiết học bị trùng thời gian. Hãy thử chọn
							lớp khác hoặc sử dụng tính năng tạo lịch tự động.
						</AlertDescription>
					</Alert>
				)}

				{/* Calendar Grid */}
				<div className="space-y-6">
					{weeks.map((week, weekIndex) => (
						<div key={weekIndex} className="border rounded-lg overflow-hidden">
							{/* Week Header */}
							<div className="bg-muted/50 p-3 border-b">
								<h3 className="font-medium">
									Tuần {weekIndex + 1}: {week[0].toLocaleDateString('vi-VN')} -{' '}
									{week[6].toLocaleDateString('vi-VN')}
								</h3>
							</div>

							{/* Days Grid */}
							<div className="grid grid-cols-7 gap-0">
								{week.map((date, dayIndex) => {
									const sessions = getSessionsForDay(date);
									const isToday = date.toDateString() === new Date().toDateString();
									const isWeekend = dayIndex === 0 || dayIndex === 6;

									return (
										<div
											key={dayIndex}
											className={`border-r last:border-r-0 grid grid-rows-[auto_1fr_1fr_1fr] ${
												isToday ? 'bg-primary/5' : isWeekend ? 'bg-muted/20' : ''
											}`}
										>
											{/* Day Header */}
											<div
												className={`p-2 border-b text-center ${
													isToday ? 'bg-primary text-primary-foreground' : 'bg-muted/30'
												}`}
											>
												<div className="text-xs font-medium">{DAYS_OF_WEEK[dayIndex]}</div>
												<div className="text-sm">{date.getDate()}</div>
											</div>

											{/* Sessions by Shifts - Each shift gets equal height */}
											{SHIFTS.map((shift, shiftIndex) => {
												const shiftSessions = getSessionsByShift(sessions, shift.sessions);

												return (
													<div
														key={shiftIndex}
														className={`border-b last:border-b-0 flex flex-col ${shift.color}`}
													>
														<div className="px-2 py-1 text-xs font-medium text-center border-b bg-background/50">
															{shift.name}
														</div>
														<div className="p-2 space-y-1 flex-1 flex flex-col justify-start min-h-[100px]">
															{shiftSessions.length > 0 ? (
																shiftSessions.map((session, sessionIndex) => (
																	<div
																		key={sessionIndex}
																		className={`text-xs p-2 rounded border ${getSessionColor(session.subjectName)}`}
																	>
																		<div
																			className="font-medium truncate"
																			title={session.subjectName}
																		>
																			{session.subjectName}
																		</div>
																		<div className="text-xs opacity-75">{session.classCode}</div>
																		<div className="flex items-center gap-1 text-xs opacity-75">
																			<Clock className="h-2 w-2" />
																			Tiết {session.startSession}
																			{session.startSession !== session.endSession &&
																				`-${session.endSession}`}
																		</div>
																	</div>
																))
															) : (
																<div className="text-xs text-muted-foreground text-center py-4 italic">
																	Không có tiết học
																</div>
															)}
														</div>
													</div>
												);
											})}
										</div>
									);
								})}
							</div>
						</div>
					))}
				</div>

				{/* Legend */}
				<div className="text-xs text-muted-foreground">
					<p>
						<strong>Chú thích:</strong>
					</p>
					<ul className="list-disc list-inside space-y-1 ml-2">
						<li>Mỗi màu đại diện cho một môn học khác nhau</li>
						<li>Tiết học được hiển thị theo thời gian thực tế</li>
						<li>Các tiết trùng nhau sẽ được cảnh báo ở trên</li>
					</ul>
				</div>
			</CardContent>
		</Card>
	);
}
